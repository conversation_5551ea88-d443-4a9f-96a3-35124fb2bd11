<template>
  <section>
    <a-card size="small" title="xxx" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="planNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('planNo')"
                  @click="handleLabelClick('planNo')"
              >
                计划书单号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.planNo"></a-input>
          </a-form-item>
          <a-form-item name="businessType" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessType')"
                  @click="handleLabelClick('businessType')"
              >
                业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.commonBusinessType" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('contractNo')"
                  @click="handleLabelClick('contractNo')"
              >
                合同号（弹窗选择，允许修改）
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="businessLocation" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessLocation')"
                  @click="handleLabelClick('businessLocation')"
              >
                业务地点（从外商合同带入，不允许修改）
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.businessLocation" id="businessLocation">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.business_location" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="buyer" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('buyer')"
                  @click="handleLabelClick('buyer')"
              >
                买家（从外商合同带入，不允许修改）
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.buyer" id="buyer">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="seller" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('seller')"
                  @click="handleLabelClick('seller')"
              >
                卖家（从外商合同带入，不允许修改）
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.seller" id="seller">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="manufacturer" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('manufacturer')"
                  @click="handleLabelClick('manufacturer')"
              >
                使用厂家（从外商合同带入）
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.manufacturer" id="manufacturer">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="domesticClient" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('domesticClient')"
                  @click="handleLabelClick('domesticClient')"
              >
                国内委托方（从外商合同带入）
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.domesticClient" id="domesticClient">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="estReceiveDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('estReceiveDate')"
                  @click="handleLabelClick('estReceiveDate')"
              >
                预计收款时间（用户录入，当收款状态为未收款时触发预警）
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.estReceiveDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="receiveStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('receiveStatus')"
                  @click="handleLabelClick('receiveStatus')"
              >
                收款状态（0未收款，1已收款。默认未收款）
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.receiveStatus" id="receiveStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.receiveStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="estPaymentDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('estPaymentDate')"
                  @click="handleLabelClick('estPaymentDate')"
              >
                预计付款时间（用户录入，当付款状态为未付款时触发预警）
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.estPaymentDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="paymentStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('paymentStatus')"
                  @click="handleLabelClick('paymentStatus')"
              >
                付款状态（0未付款，1已付款。默认未付款）
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.paymentStatus" id="paymentStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.paymentStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="estArbitrationDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('estArbitrationDate')"
                  @click="handleLabelClick('estArbitrationDate')"
              >
                预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.estArbitrationDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="arbitrationStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('arbitrationStatus')"
                  @click="handleLabelClick('arbitrationStatus')"
              >
                预裁定状态（0未裁定，1已裁定。默认未裁定）
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.arbitrationStatus" id="arbitrationStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.arbitrationStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="estLicenseDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('estLicenseDate')"
                  @click="handleLabelClick('estLicenseDate')"
              >
                预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.estLicenseDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="licenseStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('licenseStatus')"
                  @click="handleLabelClick('licenseStatus')"
              >
                许可证状态（0未办理，1已办理。默认未办理）
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.licenseStatus" id="licenseStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.licenseStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="estTransportCertDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('estTransportCertDate')"
                  @click="handleLabelClick('estTransportCertDate')"
              >
                预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.estTransportCertDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="transportCertStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('transportCertStatus')"
                  @click="handleLabelClick('transportCertStatus')"
              >
                准运证状态（0未办理，1已办理。默认未办理）
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.transportCertStatus" id="transportCertStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.transportCertStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="estInsuranceDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('estInsuranceDate')"
                  @click="handleLabelClick('estInsuranceDate')"
              >
                预计保险申办时间（用户录入，当保险状态为未办理时触发预警）
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.estInsuranceDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="insuranceStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceStatus')"
                  @click="handleLabelClick('insuranceStatus')"
              >
                保险状态（0未办理，1已办理。默认未办理）
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.insuranceStatus" id="insuranceStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.insuranceStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="estPackingInfo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('estPackingInfo')"
                  @click="handleLabelClick('estPackingInfo')"
              >
                预计装箱信息（用户录入，预估箱型及箱数）
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.estPackingInfo"></a-input>
          </a-form-item>
          <a-form-item name="remark" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('remark')"
                  @click="handleLabelClick('remark')"
              >
                备注（用户录入）
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.remark"></a-input>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import {useMerchant} from "@/view/common/useMerchant";
const { getPCode } = usePCode()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
 'id':'主键'
, 'planNo':'计划书单号'
, 'businessType':'业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）'
, 'contractNo':'合同号（弹窗选择，允许修改）'
, 'businessLocation':'业务地点（从外商合同带入，不允许修改）'
, 'buyer':'买家（从外商合同带入，不允许修改）'
, 'seller':'卖家（从外商合同带入，不允许修改）'
, 'manufacturer':'使用厂家（从外商合同带入）'
, 'domesticClient':'国内委托方（从外商合同带入）'
, 'estReceiveDate':'预计收款时间（用户录入，当收款状态为未收款时触发预警）'
, 'receiveStatus':'收款状态（0未收款，1已收款。默认未收款）'
, 'estPaymentDate':'预计付款时间（用户录入，当付款状态为未付款时触发预警）'
, 'paymentStatus':'付款状态（0未付款，1已付款。默认未付款）'
, 'estArbitrationDate':'预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）'
, 'arbitrationStatus':'预裁定状态（0未裁定，1已裁定。默认未裁定）'
, 'estLicenseDate':'预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）'
, 'licenseStatus':'许可证状态（0未办理，1已办理。默认未办理）'
, 'estTransportCertDate':'预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）'
, 'transportCertStatus':'准运证状态（0未办理，1已办理。默认未办理）'
, 'estInsuranceDate':'预计保险申办时间（用户录入，当保险状态为未办理时触发预警）'
, 'insuranceStatus':'保险状态（0未办理，1已办理。默认未办理）'
, 'entryStatus':'报关状态'
, 'estPackingInfo':'预计装箱信息（用户录入，预估箱型及箱数）'
, 'licenseNo':'许可证号'
, 'licenseApplyDate':'许可证申请日期'
, 'licenseValidityDate':'许可证有效期'
, 'licenseRemark':'许可证备注'
, 'remark':'备注（用户录入）'
, 'status':'数据状态'
, 'apprStatus':'审批状态'
, 'confirmTime':'确认时间'
, 'versionNo':'版本号'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'parentId':'父级ID'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'updateBy':'最后修改人'
, 'updateTime':'最后修改时间'
, 'createUserName':'创建人姓名'
, 'updateUserName':'最后修改人姓名'
, 'extend1':'扩展字段1'
, 'extend2':'扩展字段2'
, 'extend3':'扩展字段3'
, 'extend4':'扩展字段4'
, 'extend5':'扩展字段5'
, 'extend6':'扩展字段6'
, 'extend7':'扩展字段7'
, 'extend8':'扩展字段8'
, 'extend9':'扩展字段9'
, 'extend10':'扩展字段10'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  id:''
,planNo: ''
,businessType: ''
,contractNo: ''
,businessLocation: ''
,buyer: ''
,seller: ''
,manufacturer: ''
,domesticClient: ''
,estReceiveDate: ''
,receiveStatus: ''
,estPaymentDate: ''
,paymentStatus: ''
,estArbitrationDate: ''
,arbitrationStatus: ''
,estLicenseDate: ''
,licenseStatus: ''
,estTransportCertDate: ''
,transportCertStatus: ''
,estInsuranceDate: ''
,insuranceStatus: ''
,estPackingInfo: ''
,remark: ''
,licenseNo: ''
,licenseApplyDate: ''
,licenseValidityDate: ''
,licenseRemark: ''
})
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  // 初始化客商选项数据
  getMerchantOptions()
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.id, 'default')
})
/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'xxx',
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanHead.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核通过成功！")
              // 返回列表页面
              onBack(true)
            } else {
              message.error(res.message || '审核失败')
            }
          })
          .catch(error => {
            console.error('审核失败:', error)
            message.error('审核失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanHead.reject, params)
          .then(res => {
            if (res.code === 200) {
              // 审核退回成功后，调用标记保存接口
              return saveCurrentMarkings(formData.id, 'default', fieldMarkings.value)
            } else {
              throw new Error(res.message || '审核退回失败')
            }
          })
          .then(res => {
            message.success("审核退回成功！")
            // 返回列表页面
            onBack(true)
          })
          .catch(error => {
            console.error('审核退回失败:', error)
            message.error(error.message || '审核退回失败，请重试')
          })
          .finally(() => {
            invalidLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
<style lang="less" scoped>
</style>
