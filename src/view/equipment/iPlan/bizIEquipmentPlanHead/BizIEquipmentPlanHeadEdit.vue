<template>
  <section>
    <a-card size="small" title="进口进货计划书" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="planNo" :label="'计划书单号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.planNo"></a-input>
          </a-form-item>
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.commonBusinessType" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="businessLocation" :label="'业务地点'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.businessLocation" id="businessLocation">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.business_location" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="buyer" :label="'买家'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.buyer" id="buyer">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="seller" :label="'卖家'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.seller" id="seller">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="manufacturer" :label="'使用厂家'" class="grid-item" :colon="false">
            <cs-select  disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.manufacturer" id="manufacturer">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="domesticClient" :label="'国内委托方'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.domesticClient" id="domesticClient">
              <a-select-option class="cs-select-dropdown" v-for="item in merchantOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item class="grid-item" :colon="false" />
          <a-form-item name="estReceiveDate" :label="'预计收款时间'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.estReceiveDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="receiveStatus" :label="'收款状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.receiveStatus" id="receiveStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.receiveStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item class="grid-item" :colon="false" />
          <a-form-item name="estPaymentDate" :label="'预计付款时间'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.estPaymentDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="paymentStatus" :label="'付款状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.paymentStatus" id="paymentStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.paymentStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item class="grid-item" :colon="false" />
          <a-form-item name="estArbitrationDate" :label="'预计裁定时间'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.estArbitrationDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="arbitrationStatus" :label="'预裁定状态'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.arbitrationStatus" id="arbitrationStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.arbitrationStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item class="grid-item" :colon="false" />
          <a-form-item name="estLicenseDate" :label="'预计许可证申办时间'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.estLicenseDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="licenseStatus" :label="'许可证状态'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.licenseStatus" id="licenseStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.licenseStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item class="grid-item" :colon="false" />
          <a-form-item name="estTransportCertDate" :label="'预计准运证申办时间'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.estTransportCertDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="transportCertStatus" :label="'准运证状态'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.transportCertStatus" id="transportCertStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.transportCertStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item class="grid-item" :colon="false" />
          <a-form-item name="estInsuranceDate" :label="'预计保险申办时间'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.estInsuranceDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="insuranceStatus" :label="'保险状态'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.insuranceStatus" id="insuranceStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.insuranceStatus" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.remark"></a-input>
          </a-form-item>

          <a-form-item name="estPackingInfo" :label="'预计装箱信息'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.estPackingInfo"></a-textarea>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>

    <a-card size="small"  class="cs-card-form">
      <plan-list
        ref="detailTableRef"
        :disabled="showDisable"
        :head-id="formData.id"
        :edit-config="props.editConfig"
        @change="handleDetailChange"
      />
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {deleteEquipmentPlan, updateEquipmentPlan} from "@/api/equipment/equipmentPlanApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import PlanList from '../list/BizIEquipmentPlanListTable.vue'
import {useMerchant} from "@/view/common/useMerchant";
const { getPCode } = usePCode()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
 'id':'主键'
, 'planNo':'计划书单号'
, 'businessType':'业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）'
, 'contractNo':'合同号（弹窗选择，允许修改）'
, 'businessLocation':'业务地点（从外商合同带入，不允许修改）'
, 'buyer':'买家（从外商合同带入，不允许修改）'
, 'seller':'卖家（从外商合同带入，不允许修改）'
, 'manufacturer':'使用厂家（从外商合同带入）'
, 'domesticClient':'国内委托方（从外商合同带入）'
, 'estReceiveDate':'预计收款时间（用户录入，当收款状态为未收款时触发预警）'
, 'receiveStatus':'收款状态（0未收款，1已收款。默认未收款）'
, 'estPaymentDate':'预计付款时间（用户录入，当付款状态为未付款时触发预警）'
, 'paymentStatus':'付款状态（0未付款，1已付款。默认未付款）'
, 'estArbitrationDate':'预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）'
, 'arbitrationStatus':'预裁定状态（0未裁定，1已裁定。默认未裁定）'
, 'estLicenseDate':'预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）'
, 'licenseStatus':'许可证状态（0未办理，1已办理。默认未办理）'
, 'estTransportCertDate':'预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）'
, 'transportCertStatus':'准运证状态（0未办理，1已办理。默认未办理）'
, 'estInsuranceDate':'预计保险申办时间（用户录入，当保险状态为未办理时触发预警）'
, 'insuranceStatus':'保险状态（0未办理，1已办理。默认未办理）'
, 'entryStatus':'报关状态'
, 'estPackingInfo':'预计装箱信息（用户录入，预估箱型及箱数）'
, 'licenseNo':'许可证号'
, 'licenseApplyDate':'许可证申请日期'
, 'licenseValidityDate':'许可证有效期'
, 'licenseRemark':'许可证备注'
, 'remark':'备注（用户录入）'
, 'status':'数据状态'
, 'apprStatus':'审批状态'
, 'confirmTime':'确认时间'
, 'versionNo':'版本号'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'parentId':'父级ID'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'updateBy':'最后修改人'
, 'updateTime':'最后修改时间'
, 'createUserName':'创建人姓名'
, 'updateUserName':'最后修改人姓名'
, 'extend1':'扩展字段1'
, 'extend2':'扩展字段2'
, 'extend3':'扩展字段3'
, 'extend4':'扩展字段4'
, 'extend5':'扩展字段5'
, 'extend6':'扩展字段6'
, 'extend7':'扩展字段7'
, 'extend8':'扩展字段8'
, 'extend9':'扩展字段9'
, 'extend10':'扩展字段10'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
//首次新增未保存 删除数据
let firstAddSave = ref(false);
const formData = reactive({
  id: ''
,planNo: ''
,businessType: ''
,contractNo: ''
,businessLocation: ''
,buyer: ''
,seller: ''
,manufacturer: ''
,domesticClient: ''
,estReceiveDate: ''
,receiveStatus: ''
,estPaymentDate: ''
,paymentStatus: ''
,estArbitrationDate: ''
,arbitrationStatus: ''
,estLicenseDate: ''
,licenseStatus: ''
,estTransportCertDate: ''
,transportCertStatus: ''
,estInsuranceDate: ''
,insuranceStatus: ''
,estPackingInfo: ''
,remark: ''
,licenseNo: ''
,licenseApplyDate: ''
,licenseValidityDate: ''
,licenseRemark: ''
})
// 表单校验规则
const rules = {
planNo: [
  { required: true, message: '计划书单号不能为空！', trigger: 'blur' },
{ max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
businessType: [
{ max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
contractNo: [
  { required: true, message: '合同号不能为空！', trigger: 'blur' },
{ max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
businessLocation: [
  { required: true, message: '业务地点不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
buyer: [
  { required: true, message: '买家不能为空！', trigger: 'blur' },
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
seller: [
  { required: true, message: '卖家不能为空！', trigger: 'blur' },
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
manufacturer: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
domesticClient: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
estReceiveDate: [
{ max: 6, message: '长度不能超过6位字节(汉字占2位)！', trigger: 'blur' }
],
receiveStatus: [
  { required: true, message: '收款状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
estPaymentDate: [
{ max: 6, message: '长度不能超过6位字节(汉字占2位)！', trigger: 'blur' }
],
paymentStatus: [
  { required: true, message: '付款状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
estArbitrationDate: [
{ max: 6, message: '长度不能超过6位字节(汉字占2位)！', trigger: 'blur' }
],
arbitrationStatus: [
  { required: true, message: '预裁定状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
estLicenseDate: [
{ max: 6, message: '长度不能超过6位字节(汉字占2位)！', trigger: 'blur' }
],
licenseStatus: [
  { required: true, message: '许可证状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
estTransportCertDate: [
{ max: 6, message: '长度不能超过6位字节(汉字占2位)！', trigger: 'blur' }
],
transportCertStatus: [
  { required: true, message: '准运证状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
estInsuranceDate: [
{ max: 6, message: '长度不能超过6位字节(汉字占2位)！', trigger: 'blur' }
],
insuranceStatus: [
  { required: true, message: '保险状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
estPackingInfo: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
remark: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
licenseNo: [
{ max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
licenseRemark: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
}
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave.value) {
    // 新增状态且未保存，提示用户并删除数据
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      content: '这票数据还没有保存，确定要返回吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        // 调用删除接口
        deleteEquipmentPlan(formData.id).then(res => {
          if (res.code === 200) {
            emit('onEditBack', val);
          } else {
            message.error(res.message || '删除失败');
          }
        }).catch(error => {
          console.error('删除失败:', error);
          message.error('删除失败，请重试');
        });
      },
      onCancel() {
        // 用户取消，不做任何操作
      }
    });
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
// 处理明细数据变化
const handleDetailChange = (details) => {
  formData.details = details;
};
// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      updateEquipmentPlan(formData.id, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
          firstAddSave.value = true
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      updateEquipmentPlan(formData.id, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
const pCode = ref('')
// 表格引用
const detailTableRef = ref();
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  // 初始化客商选项数据
  getMerchantOptions()
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  // getByidAndFormType(formData.id, 'default')
})
</script>
<style lang="less" scoped>
</style>
